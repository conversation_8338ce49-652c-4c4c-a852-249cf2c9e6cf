"use client";

import {
  useState,
  useEffect,
  useMemo,
  useCallback,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import {
  getCoreRowModel,
  useReactTable,
  RowSelectionState,
  ColumnFiltersState,
} from "@tanstack/react-table";
import { DataTable } from "@/components/data-table/data-table";
import { DataTableToolbar } from "@/components/data-table/data-table-toolbar";
import { DocumentsTableActionBar } from "./documents-table-action-bar";
import { createDocumentsColumns } from "./documents-columns";
import { ContractsV1Files } from "@askinfosec/types";
import { getAllDocuments, GetDocumentsFilters } from "../_actions/get-all-documents";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton";
import { downloadDocument } from "../_actions/download-document";
import { updateDocumentAction } from "../_actions/update-document";
import { deleteDocuments } from "../_actions/delete-documents";
import { ConfirmDelete } from "@/components/confirm-delete";
import { useConfirmDelete } from "@/hooks/use-confirm-delete";
import { toast } from "sonner";
import { DocumentEditorContainer } from "./document-editor/document-editor-container";

interface DocumentsDataTableProps {
  organizationId: string;
  initialFilters?: GetDocumentsFilters;
}

export interface DocumentsDataTableRef {
  openDocument: (documentId: string) => void;
  addDocument: (document: ContractsV1Files.FileDTO) => void;
}

export const DocumentsDataTable = forwardRef<DocumentsDataTableRef, DocumentsDataTableProps>(
  ({ organizationId, initialFilters }, ref) => {
    const [data, setData] = useState<ContractsV1Files.FileDTO[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
    // Track which document editor sheet is open, so re-renders don't close it
    const [openDocId, setOpenDocId] = useState<string | null>(null);

    // Bulk delete functionality
    const {
      isOpen: isBulkDeleteOpen,
      entities: bulkDeleteEntities,
      entityType: bulkDeleteEntityType,
      openDialog: openBulkDeleteDialog,
      closeDialog: closeBulkDeleteDialog,
    } = useConfirmDelete();
    const [isBulkDeleting, setIsBulkDeleting] = useState(false);

    // Single delete functionality
    const {
      isOpen: isSingleDeleteOpen,
      entities: singleDeleteEntities,
      entityType: singleDeleteEntityType,
      openDialog: openSingleDeleteDialog,
      closeDialog: closeSingleDeleteDialog,
    } = useConfirmDelete();
    const [isSingleDeleting, setIsSingleDeleting] = useState(false);

    // Caching and debouncing
    const [cache, setCache] = useState<Map<string, ContractsV1Files.FileDTO[]>>(new Map());
    const [isFiltering, setIsFiltering] = useState(false);
    const debounceTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
    const lastServerFiltersRef = useRef<GetDocumentsFilters>({});

    // Expose methods to parent component
    useImperativeHandle(
      ref,
      () => ({
        openDocument: (documentId: string) => {
          setOpenDocId(documentId);
        },
        addDocument: (document: ContractsV1Files.FileDTO) => {
          setData((prevData) => [document, ...prevData]);
        },
      }),
      [],
    );

    // Cache management
    const MAX_CACHE_SIZE = 10;
    const addToCache = useCallback((key: string, data: ContractsV1Files.FileDTO[]) => {
      setCache((prev) => {
        const newCache = new Map(prev);
        newCache.set(key, data);

        // Remove oldest entries if cache is too large
        if (newCache.size > MAX_CACHE_SIZE) {
          const firstKey = newCache.keys().next().value;
          if (firstKey !== undefined) {
            newCache.delete(firstKey);
          }
        }

        return newCache;
      });
    }, []);

    // Generate cache key for server filters
    const generateCacheKey = useCallback((filters: GetDocumentsFilters): string => {
      const sortedFilters = Object.keys(filters)
        .sort()
        .reduce(
          (acc, key) => {
            const value = filters[key as keyof GetDocumentsFilters];
            if (value !== undefined && value !== null && value !== "") {
              if (Array.isArray(value)) {
                acc[key] = value.sort();
              } else {
                acc[key] = value;
              }
            }
            return acc;
          },
          {} as Record<string, unknown>,
        );

      return JSON.stringify(sortedFilters);
    }, []);

    // Convert column filters to server-side filters
    const convertFiltersToServerFilters = useCallback(
      (filters: ColumnFiltersState): GetDocumentsFilters => {
        const serverFilters: GetDocumentsFilters = { ...initialFilters };

        // Track which filters are present in the column filters
        const presentFilters = new Set(filters.map((f) => f.id));

        // Explicitly clear filters that are not present
        if (!presentFilters.has("name")) {
          delete serverFilters.search;
        }
        if (!presentFilters.has("documentType")) {
          delete serverFilters.documentType;
        }
        if (!presentFilters.has("accessLevel")) {
          delete serverFilters.accessLevel;
        }
        if (!presentFilters.has("createdAt")) {
          delete serverFilters.createdAfter;
          delete serverFilters.createdBefore;
        }

        filters.forEach((filter) => {
          const { id, value } = filter;

          switch (id) {
            case "name":
              if (typeof value === "string" && value.trim()) {
                serverFilters.search = value.trim();
              } else {
                delete serverFilters.search;
              }
              break;
            case "documentType":
              if (Array.isArray(value) && value.length > 0) {
                serverFilters.documentType = value;
              } else {
                delete serverFilters.documentType;
              }
              break;
            case "accessLevel":
              if (Array.isArray(value) && value.length > 0) {
                serverFilters.accessLevel = value;
              } else {
                delete serverFilters.accessLevel;
              }
              break;
            case "tags":
              if (Array.isArray(value) && value.length > 0) {
                // For tags, we might need to implement a different approach
                // since the current API doesn't have a direct tags filter
                // For now, we'll handle this client-side
              }
              break;
            case "createdAt":
              if (value && typeof value === "object" && "from" in value) {
                const dateRange = value as { from?: string; to?: string };
                if (dateRange.from) serverFilters.createdAfter = dateRange.from;
                if (dateRange.to) serverFilters.createdBefore = dateRange.to;
              } else {
                delete serverFilters.createdAfter;
                delete serverFilters.createdBefore;
              }
              break;
          }
        });

        return serverFilters;
      },
      [initialFilters],
    );

    // Load documents function with caching
    const loadDocuments = useCallback(
      async (customFilters?: GetDocumentsFilters, useCache: boolean = true) => {
        try {
          setLoading(true);
          const filtersToUse = customFilters || convertFiltersToServerFilters(columnFilters);
          const cacheKey = generateCacheKey(filtersToUse);

          // Check cache first
          if (useCache && cache.has(cacheKey)) {
            const cachedData = cache.get(cacheKey)!;
            setData(cachedData);
            setError(null);
            setLoading(false);
            return;
          }

          const result = await getAllDocuments(organizationId, filtersToUse);

          if (result.error) {
            setError(result.error);
          } else {
            setData(result.files);
            setError(null);

            // Cache the result with size management
            addToCache(cacheKey, result.files);
          }
        } catch (err) {
          setError("Failed to load documents");
          console.error("Error loading documents:", err);
        } finally {
          setLoading(false);
        }
      },
      [
        organizationId,
        columnFilters,
        convertFiltersToServerFilters,
        cache,
        generateCacheKey,
        addToCache,
      ],
    );

    // Debounced filter handler
    const handleFilterChange = useCallback(() => {
      // Clear existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // Set loading state for immediate feedback
      setIsFiltering(true);

      // Debounce the actual filter application
      debounceTimeoutRef.current = setTimeout(() => {
        const serverFilters = convertFiltersToServerFilters(columnFilters);
        const cacheKey = generateCacheKey(serverFilters);

        // Check if we need to make a server request
        const currentFiltersString = JSON.stringify(serverFilters);
        const lastFiltersString = JSON.stringify(lastServerFiltersRef.current);
        const needsServerRequest =
          !cache.has(cacheKey) || currentFiltersString !== lastFiltersString;

        if (needsServerRequest) {
          lastServerFiltersRef.current = serverFilters;
          loadDocuments(serverFilters, true);
        } else {
          // Use cached data
          const cachedData = cache.get(cacheKey)!;
          setData(cachedData);
          setError(null);
        }

        setIsFiltering(false);
      }, 300); // 300ms debounce
    }, [columnFilters, convertFiltersToServerFilters, generateCacheKey, cache, loadDocuments]);

    // Load documents on component mount
    useEffect(() => {
      loadDocuments();
    }, [organizationId, initialFilters]);

    // Handle filter changes with debouncing
    useEffect(() => {
      // Always call handleFilterChange when columnFilters change
      // The function itself will determine if a server request is needed
      handleFilterChange();
    }, [columnFilters, handleFilterChange]);

    // Cleanup timeout on unmount
    useEffect(() => {
      return () => {
        if (debounceTimeoutRef.current) {
          clearTimeout(debounceTimeoutRef.current);
        }
      };
    }, []);

    // Add keyboard shortcut for search focus (Cmd+K)
    useEffect(() => {
      const handleKeyDown = (event: KeyboardEvent) => {
        // Check for Cmd+K (Mac) or Ctrl+K (Windows/Linux)
        if ((event.metaKey || event.ctrlKey) && event.key === "k") {
          event.preventDefault();

          // Find the search input element
          const searchInput = document.querySelector(
            'input[placeholder*="Search documents"]',
          ) as HTMLInputElement;
          if (searchInput) {
            searchInput.focus();
          }
        }
      };

      // Add event listener
      document.addEventListener("keydown", handleKeyDown);

      // Cleanup
      return () => {
        document.removeEventListener("keydown", handleKeyDown);
      };
    }, []);

    // Apply client-side filtering for tags
    const filteredData = useMemo(() => {
      const tagsFilter = columnFilters.find((filter) => filter.id === "tags");
      if (!tagsFilter || !Array.isArray(tagsFilter.value) || tagsFilter.value.length === 0) {
        return data;
      }

      const selectedTags = tagsFilter.value as string[];
      return data.filter((doc) => {
        const docTags = doc.tags || [];
        return selectedTags.some((tag) => docTags.includes(tag));
      });
    }, [data, columnFilters]);

    const handleDeleteSelected = () => {
      const selectedRows = table.getFilteredSelectedRowModel().rows;
      const selectedFiles = selectedRows.map((row) => ({
        id: row.original.id,
        name: row.original.name,
      }));

      if (selectedFiles.length === 0) return;

      // Open confirm delete dialog for multiple files
      openBulkDeleteDialog(selectedFiles, "file");
    };

    const handleBulkDelete = async () => {
      setIsBulkDeleting(true);
      try {
        const fileIds = bulkDeleteEntities.map((entity) => entity.id);
        const result = await deleteDocuments({
          organizationId,
          fileIds,
        });

        if (result.success) {
          toast.success(
            result.message ||
              `Successfully deleted ${fileIds.length} file${fileIds.length > 1 ? "s" : ""}`,
          );
          // Clear selection and refresh data
          setRowSelection({});
          await loadDocuments();
          // Close dialog after successful delete
          closeBulkDeleteDialog();
        } else {
          toast.error(result.message || "Failed to delete files");
        }
      } catch (error) {
        console.error("Bulk delete error:", error);
        toast.error("Failed to delete files");
      } finally {
        setIsBulkDeleting(false);
      }
    };

    // Single delete: open dialog from row action
    const onRequestDelete = (file: ContractsV1Files.FileDTO) => {
      openSingleDeleteDialog([{ id: file.id, name: file.name }], "file");
    };

    const handleSingleDelete = async () => {
      setIsSingleDeleting(true);
      try {
        const fileIds = singleDeleteEntities.map((e) => e.id);
        if (fileIds.length === 0) {
          toast.error("No file selected for deletion");
          return;
        }
        const result = await deleteDocuments({ organizationId, fileIds });
        if (result.success) {
          toast.success(result.message || "Document deleted successfully");
          await loadDocuments();
          closeSingleDeleteDialog();
        } else {
          toast.error(result.message || "Failed to delete document");
        }
      } catch (error) {
        console.error("Single delete error:", error);
        toast.error("Failed to delete document");
      } finally {
        setIsSingleDeleting(false);
      }
    };

    // Action handlers moved from columns to parent component
    const handleDownloadDocument = async (fileId: string) => {
      try {
        const result = await downloadDocument(organizationId, fileId);

        if (!result.success) {
          toast.error(result.error || "Failed to download document");
          return;
        }

        if (result.data) {
          // Convert base64 string to blob and download
          const { buffer, filename } = result.data;

          // Convert base64 to binary string using browser's atob
          const binaryString = window.atob(buffer);
          const bytes = new Uint8Array(binaryString.length);

          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
          }

          const blob = new Blob([bytes], {
            type: "application/octet-stream",
          });
          const url = window.URL.createObjectURL(blob);

          const link = document.createElement("a");
          link.href = url;
          link.download = filename;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          toast.success("Document downloaded successfully");
        }
      } catch (error) {
        console.error("Download error:", error);
        toast.error("Failed to download document");
      }
    };

    const handleUpdateDocument = async (
      fileId: string,
      updatedDocument: Partial<ContractsV1Files.FileDTO>,
    ) => {
      try {
        const res = await updateDocumentAction({
          organizationId,
          fileId,
          ...updatedDocument,
        });

        if (!res?.success) {
          toast.error(res?.message || "Failed to update document");
          return;
        }

        toast.success("Document updated successfully");

        // Refresh the data to show updated document
        await loadDocuments();
      } catch (error) {
        console.error("Update error:", error);
        toast.error("Failed to update document");
      }
    };

    // Receive UI-only local update events (e.g., rename) from editor without refetch
    const handleLocalUpdate = async (
      updated: Partial<ContractsV1Files.FileDTO> & { id: string },
    ) => {
      setData((prev) => prev.map((doc) => (doc.id === updated.id ? { ...doc, ...updated } : doc)));
      // TODO: Update the document in the database
      await handleUpdateDocument(updated.id, updated);
    };

    const handleDownloadSelected = () => {
      // This will be called after table is created
      const selectedIds = Object.keys(rowSelection).filter((key) => rowSelection[key]);

      // TODO: Implement bulk download functionality
      console.log("Downloading documents:", selectedIds);
      setRowSelection({});
    };

    // Get columns from the extracted columns file with action handlers
    const tagOptions = useMemo(() => {
      const set = new Set<string>();
      for (const d of data) {
        for (const t of d.tags ?? []) set.add(t);
      }
      return Array.from(set)
        .sort((a, b) => a.localeCompare(b))
        .map((t) => ({ label: t, value: t }));
    }, [data]);

    const columns = createDocumentsColumns({
      onDownload: handleDownloadDocument,
      onRequestDelete,
      tagOptions,
      onDocOpenChange: setOpenDocId,
    });

    const table = useReactTable({
      data: filteredData,
      columns,
      getCoreRowModel: getCoreRowModel(),
      enableRowSelection: true,
      enableColumnFilters: true,
      manualFiltering: true, // Disable client-side filtering since we're doing server-side
      state: {
        rowSelection,
        columnFilters,
      },
      onRowSelectionChange: (updater) => {
        const newSelection = typeof updater === "function" ? updater(rowSelection) : updater;
        setRowSelection(newSelection);
      },
      onColumnFiltersChange: setColumnFilters,
    });

    if (loading && !isFiltering) {
      return (
        <DataTableSkeleton
          columnCount={columns.length}
          rowCount={3}
          filterCount={3}
          cellWidths={["20rem", "8rem", "10rem", "8rem", "10rem"]}
          shrinkZero={true}
        />
      );
    }

    if (error) {
      return (
        <div className="flex items-center justify-center h-32">
          <div className="text-sm text-red-500">{error}</div>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {/* Hoisted editor container so it persists across table rerenders */}
        {openDocId && (
          <DocumentEditorContainer
            document={data.find((d) => d.id === openDocId)!}
            organizationId={organizationId}
            onSave={(updated) => handleUpdateDocument(openDocId, updated)}
            onLocalUpdate={handleLocalUpdate}
            open={true}
            onOpenChange={(isOpen) => setOpenDocId(isOpen ? openDocId : null)}
          />
        )}

        <DataTable
          table={table}
          actionBar={
            <DocumentsTableActionBar
              table={table}
              onDeleteSelected={handleDeleteSelected}
              onDownloadSelected={handleDownloadSelected}
            />
          }
          className="space-y-4"
        >
          <DataTableToolbar table={table} />
        </DataTable>

        {/* Bulk delete confirmation dialog */}
        <ConfirmDelete
          isOpen={isBulkDeleteOpen}
          onClose={closeBulkDeleteDialog}
          onConfirm={handleBulkDelete}
          entities={bulkDeleteEntities}
          entityType={bulkDeleteEntityType}
          isLoading={isBulkDeleting}
        />

        {/* Single delete confirmation dialog */}
        <ConfirmDelete
          isOpen={isSingleDeleteOpen}
          onClose={closeSingleDeleteDialog}
          onConfirm={handleSingleDelete}
          entities={singleDeleteEntities}
          entityType={singleDeleteEntityType}
          isLoading={isSingleDeleting}
        />
      </div>
    );
  },
);

DocumentsDataTable.displayName = "DocumentsDataTable";
