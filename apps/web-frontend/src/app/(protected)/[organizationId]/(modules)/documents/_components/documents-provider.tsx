"use client";

import React, { createContext, useCallback, useContext, useMemo, useState } from "react";
import { ContractsV1Files } from "@askinfosec/types";
import {
  createDocument as createDocumentAction,
  CreateDocumentParams,
  CreateDocumentResponse,
} from "../_actions/create-document";
import { updateDocumentAction, UpdateDocumentParams } from "../_actions/update-document";
import {
  deleteDocuments as deleteDocumentsAction,
  DeleteDocumentsResponse,
} from "../_actions/delete-documents";
import { downloadDocument as downloadDocumentAction } from "../_actions/download-document";

interface DocumentsContextValue {
  organizationId: string;
  // Central refresh signal for readers (e.g., table) to react and refetch
  refreshVersion: number;
  requestRefresh: () => void;

  // Local add/open signals for table to react without ref coupling
  lastAdded?: ContractsV1Files.FileDTO;
  lastAddedVersion: number;
  addDocumentLocal: (doc: ContractsV1Files.FileDTO) => void;

  openDocumentId?: string;
  openVersion: number;
  openDocumentLocal: (id: string) => void;

  // CRUD surface
  create: (params: CreateDocumentParams) => Promise<CreateDocumentResponse>;
  update: (params: UpdateDocumentParams) => Promise<ContractsV1Files.FileUpdateResponse>;
  remove: (fileIds: string[]) => Promise<DeleteDocumentsResponse>;
  download: (fileId: string) => ReturnType<typeof downloadDocumentAction>;
}

const DocumentsContext = createContext<DocumentsContextValue | undefined>(undefined);

interface DocumentsProviderProps {
  organizationId: string;
  children: React.ReactNode;
}

export function DocumentsProvider({ organizationId, children }: DocumentsProviderProps) {
  const [refreshVersion, setRefreshVersion] = useState(0);
  const [lastAdded, setLastAdded] = useState<ContractsV1Files.FileDTO | undefined>(undefined);
  const [lastAddedVersion, setLastAddedVersion] = useState(0);
  const [openDocumentId, setOpenDocumentId] = useState<string | undefined>(undefined);
  const [openVersion, setOpenVersion] = useState(0);

  const requestRefresh = useCallback(() => {
    setRefreshVersion((v) => v + 1);
  }, []);

  const addDocumentLocal = useCallback((doc: ContractsV1Files.FileDTO) => {
    setLastAdded(doc);
    setLastAddedVersion((v) => v + 1);
  }, []);

  const openDocumentLocal = useCallback((id: string) => {
    setOpenDocumentId(id);
    setOpenVersion((v) => v + 1);
  }, []);

  const create = useCallback<DocumentsContextValue["create"]>(
    async (params) => {
      const result = await createDocumentAction(params);
      if (result.success && result.document) {
        addDocumentLocal(result.document);
        openDocumentLocal(result.document.id);
        requestRefresh();
      }
      return result;
    },
    [addDocumentLocal, openDocumentLocal, requestRefresh],
  );

  const update = useCallback<DocumentsContextValue["update"]>(
    async (params) => {
      const result = await updateDocumentAction(params);
      // Always request a refresh to keep any views in sync
      requestRefresh();
      return result;
    },
    [requestRefresh],
  );

  const remove = useCallback<DocumentsContextValue["remove"]>(
    async (fileIds) => {
      const result = await deleteDocumentsAction({ organizationId, fileIds });
      if (result.success) {
        requestRefresh();
      }
      return result;
    },
    [organizationId, requestRefresh],
  );

  const download = useCallback<DocumentsContextValue["download"]>(
    async (fileId) => {
      return downloadDocumentAction(organizationId, fileId);
    },
    [organizationId],
  );

  const value = useMemo<DocumentsContextValue>(
    () => ({
      organizationId,
      refreshVersion,
      requestRefresh,
      lastAdded,
      lastAddedVersion,
      addDocumentLocal,
      openDocumentId,
      openVersion,
      openDocumentLocal,
      create,
      update,
      remove,
      download,
    }),
    [
      organizationId,
      refreshVersion,
      requestRefresh,
      lastAdded,
      lastAddedVersion,
      addDocumentLocal,
      openDocumentId,
      openVersion,
      openDocumentLocal,
      create,
      update,
      remove,
      download,
    ],
  );

  return <DocumentsContext.Provider value={value}>{children}</DocumentsContext.Provider>;
}

export function useDocumentsContext(): DocumentsContextValue {
  const ctx = useContext(DocumentsContext);
  if (!ctx) {
    throw new Error("useDocumentsContext must be used within a DocumentsProvider");
  }
  return ctx;
}
