"use client";

import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Ellipsis, Download, Edit } from "lucide-react";
import { ContractsV1Files } from "@askinfosec/types";
import { DeleteButton } from "./delete-button";
// Editor is hoisted to table parent to avoid unmounting on data refresh

// Helper function to create options from enum schema
const createOptionsFromEnum = (enumObject: Record<string, string>) => {
  return Object.values(enumObject).map((value) => ({
    label: value.charAt(0).toUpperCase() + value.slice(1),
    value: value,
  }));
};

interface CreateDocumentsColumnsProps {
  onDownload: (fileId: string) => Promise<void>;
  onRequestDelete: (file: ContractsV1Files.FileDTO) => void; // Ask parent to open confirm dialog
  tagOptions?: { label: string; value: string }[];
  onDocOpenChange?: (id: string | null) => void;
}

export const createDocumentsColumns = ({
  onDownload,
  onRequestDelete,
  tagOptions,
  onDocOpenChange,
}: CreateDocumentsColumnsProps): ColumnDef<ContractsV1Files.FileDTO>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="translate-y-0.5"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="translate-y-0.5"
      />
    ),
    enableSorting: false,
    enableHiding: false,
    size: 40,
  },
  {
    accessorKey: "name",
    header: "Document Name",
    meta: {
      label: "Document Name",
      variant: "text",
      placeholder: "⌘K Search documents...",
    },
    cell: ({ row }) => {
      const name = row.getValue("name") as string;
      return (
        <div className="font-medium max-w-[200px] truncate" title={name}>
          {name}
        </div>
      );
    },
  },
  {
    accessorKey: "documentType",
    header: "Type",
    meta: {
      label: "Document Type",
      variant: "select",
      options: createOptionsFromEnum(ContractsV1Files.DocumentTypeSchema.enum),
    },
    cell: ({ row }) => {
      const documentType = row.getValue("documentType") as string;
      if (!documentType) return null;
      return (
        <span className="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
          {documentType}
        </span>
      );
    },
  },
  {
    accessorKey: "accessLevel",
    header: "Access Level",
    meta: {
      label: "Access Level",
      variant: "select",
      options: createOptionsFromEnum(ContractsV1Files.FileAccessLevelSchema.enum),
    },
    cell: ({ row }) => {
      const accessLevel = row.getValue("accessLevel") as string;
      if (!accessLevel) return null;

      const getAccessLevelColor = (level: string) => {
        switch (level) {
          case "public":
            return "bg-green-50 text-green-700 ring-green-600/20";
          case "internal":
            return "bg-yellow-50 text-yellow-700 ring-yellow-600/20";
          case "confidential":
            return "bg-orange-50 text-orange-700 ring-orange-600/20";
          case "restricted":
            return "bg-red-50 text-red-700 ring-red-600/20";
          default:
            return "bg-gray-50 text-gray-700 ring-gray-600/20";
        }
      };

      return (
        <span
          className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ring-1 ring-inset ${getAccessLevelColor(accessLevel)}`}
        >
          {accessLevel}
        </span>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: "Created",
    meta: {
      label: "Created Date",
      variant: "date",
    },
    cell: ({ row }) => {
      const createdAt = row.getValue("createdAt") as string;
      return (
        <div className="text-sm text-gray-600">{new Date(createdAt).toLocaleDateString()}</div>
      );
    },
  },
  {
    accessorKey: "tags",
    header: "Tags",
    meta: {
      label: "Tags",
      variant: "multiSelect",
      options: tagOptions ?? [],
    },
    cell: ({ row }) => {
      const tags = (row.getValue("tags") as string[] | undefined) ?? [];
      if (!tags.length) {
        return null;
      }
      const shown = tags.slice(0, 3);
      const overflow = tags.length - shown.length;
      return (
        <div className="flex items-center gap-1 max-w-[260px] truncate">
          {shown.map((tag, idx) => (
            <Badge key={`${tag}-${idx}`} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
          {overflow > 0 && (
            <Badge variant="outline" className="text-xs">
              +{overflow}
            </Badge>
          )}
        </div>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const handleDownload = () => onDownload(row.original.id);

      return (
        <div className="flex items-center gap-1.5">
          <Button
            aria-label="Edit document"
            variant="ghost"
            size="sm"
            onClick={() => onDocOpenChange?.(row.original.id)}
            className="h-8 w-8 p-0 hover:!bg-accent/50 hover:!text-accent-foreground"
          >
            <Edit className="h-4 w-4" />
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                aria-label="Open menu"
                variant="ghost"
                className="flex size-8 p-0 data-[state=open]:bg-muted hover:!bg-accent/50 hover:!text-accent-foreground"
              >
                <Ellipsis className="size-4" aria-hidden="true" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-40">
              <DropdownMenuItem onSelect={handleDownload}>
                <Download className="mr-2 h-4 w-4" />
                Download
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DeleteButton file={row.original} onRequestDelete={onRequestDelete} />
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    },
    size: 80,
  },
];
