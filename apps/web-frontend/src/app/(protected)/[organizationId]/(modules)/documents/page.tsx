import { getServerSession } from "@/lib/auth-server";
import { redirect } from "next/navigation";
import { PageMetadataSetter } from "@/components/layout/page-metadata-setter";
import { DocumentsManager } from "./_components/documents-manager";
import React from "react";
import { Shell } from "@/components/shell";

interface DocumentsPageProps {
  params: {
    organizationId: string;
  };
}

export default async function DocumentsPage({ params }: DocumentsPageProps) {
  const session = await getServerSession();
  const { organizationId } = await Promise.resolve(params);

  if (!session) {
    redirect("/sign-in");
  }

  return (
    <>
      <PageMetadataSetter
        title="Documents Hub"
        description="Comprehensive document management and compliance oversight for your organization."
      />

      <Shell className="gap-8">
        <DocumentsManager organizationId={organizationId} />
      </Shell>
    </>
  );
}
