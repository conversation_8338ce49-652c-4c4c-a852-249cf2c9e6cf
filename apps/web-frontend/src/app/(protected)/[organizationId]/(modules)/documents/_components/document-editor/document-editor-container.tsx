"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>rigger } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Edit, PanelLeft, PanelRight, X, Save, XCircle } from "lucide-react";
import { DocumentEditor } from "./document-editor";
import { DocumentDetailsPanel } from "./document-details-panel";
import { ContractsV1Files } from "@askinfosec/types";
import { cn } from "@/lib/utils";
import { DocumentRename } from "./document-rename";

interface DocumentEditorContainerProps {
  document: ContractsV1Files.FileDTO;
  organizationId: string;
  onSave?: (updatedDocument: Partial<ContractsV1Files.FileDTO>) => void;
  // Bubble UI-only updates upward (e.g., rename) so parent lists can update without refetch
  onLocalUpdate?: (updated: Partial<ContractsV1Files.FileDTO> & { id: string }) => void;
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export const DocumentEditorContainer = ({
  document,
  organizationId,
  onSave,
  onLocalUpdate,
  trigger,
  open,
  onOpenChange,
}: DocumentEditorContainerProps) => {
  // Local shadow copy to reflect immediate UI updates (e.g., rename) without server roundtrip
  const [localDoc, setLocalDoc] = useState<ContractsV1Files.FileDTO>(document);
  const [isOpen, setIsOpen] = useState(false);
  const [isPanelOpen, setIsPanelOpen] = useState(true);
  const [triggerSave, setTriggerSave] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [editorSessionId, setEditorSessionId] = useState(0);

  const handleOpenChange = (newOpen: boolean) => {
    if (onOpenChange) {
      onOpenChange(newOpen);
    } else {
      setIsOpen(newOpen);
    }
    if (newOpen) {
      // Starting a fresh session: reset dirty state and bump session id
      setIsDirty(false);
      setEditorSessionId((n) => n + 1);
    }
  };

  // Listen for dirty state from child editor (via CustomEvent to avoid prop drilling changes)
  useEffect(() => {
    if (typeof window === "undefined") return;
    const onDirty = (e: unknown) => {
      const custom = e as { detail?: { dirty?: boolean } };
      setIsDirty(custom?.detail?.dirty ?? false);
    };
    window.addEventListener("document-editor:dirty", onDirty as EventListener);
    return () => window.removeEventListener("document-editor:dirty", onDirty as EventListener);
  }, []);

  const handleSave = (updatedDocument?: Partial<ContractsV1Files.FileDTO>) => {
    const payload: Partial<ContractsV1Files.FileDTO> = {
      ...(updatedDocument || {}),
    };
    // If name changed via inline rename, include it in save payload
    if (localDoc.name && localDoc.name !== document.name) {
      payload.name = localDoc.name;
    }
    // Fire parent save in the background; keep sheet open
    onSave?.(payload);
    // Reset dirty flag so Save/Cancel buttons reflect current state
    setIsDirty(false);
  };

  const handleSaveClick = () => {
    setTriggerSave(true);
    // Reset trigger after a brief moment
    setTimeout(() => setTriggerSave(false), 100);
  };

  const handleCancel = () => {
    if (isDirty) {
      const confirmLeave = window.confirm(
        "You have unsaved changes. Do you really want to discard them?",
      );
      if (!confirmLeave) return;
    }
    // Close and ensure next open is a fresh session
    setIsDirty(false);
    handleOpenChange(false);
  };

  const togglePanel = () => {
    setIsPanelOpen(!isPanelOpen);
  };

  const isControlled = open !== undefined;
  const sheetOpen = isControlled ? open : isOpen;

  return (
    <Sheet open={sheetOpen} onOpenChange={handleOpenChange}>
      {!isControlled && (
        <SheetTrigger asChild>
          {trigger || (
            <Button variant="outline" size="sm">
              <Edit className="mr-2 h-4 w-4" />
              Edit Document
            </Button>
          )}
        </SheetTrigger>
      )}
      <SheetContent
        className={cn(
          "[&>button]:hidden transition-all duration-300 ease-in-out p-0",
          isPanelOpen ? "!w-screen !max-w-none" : "!w-[75vw] !max-w-none",
        )}
        side="left"
      >
        <SheetHeader className="flex flex-row items-center justify-between p-4 pb-0">
          <SheetTitle className="text-lg font-semibold">
            <DocumentRename
              documentName={localDoc.name || "Untitled Document"}
              onRename={(newName) => {
                // Update local shadow state so Sheet title and details panel reflect immediately
                setLocalDoc((prev) => ({ ...prev, name: newName }));
                // Bubble event upward so table row can update without refetch
                onLocalUpdate?.({ id: document.id, name: newName });
              }}
            />
          </SheetTitle>
          <div className="flex items-center gap-2">
            {/* Primary Actions */}
            <div className="flex items-center gap-2 mr-2">
              <Button
                variant="default"
                size="sm"
                onClick={handleSaveClick}
                className="h-8 px-3"
                title="Save changes"
                disabled={!isDirty}
              >
                <Save className="h-4 w-4 mr-1" />
                Save
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancel}
                className="h-8 px-3"
                title="Cancel editing"
                disabled={!isDirty}
              >
                <XCircle className="h-4 w-4 mr-1" />
                Cancel
              </Button>
            </div>

            {/* Secondary Actions */}
            <div className="flex items-center gap-1 border-l border-border pl-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={togglePanel}
                className="h-8 w-8 p-0 hover:!bg-accent/50 hover:!text-accent-foreground"
                title={isPanelOpen ? "Hide details panel" : "Show details panel"}
              >
                {isPanelOpen ? (
                  <PanelRight className="h-4 w-4" />
                ) : (
                  <PanelLeft className="h-4 w-4" />
                )}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleOpenChange(false)}
                className="h-8 w-8 p-0 hover:!bg-accent/50 hover:!text-accent-foreground"
                title="Close editor"
                onMouseDown={(e) => {
                  // Intercept close when dirty; use onMouseDown to run before focus changes
                  if (isDirty) {
                    e.preventDefault();
                    const confirmLeave = window.confirm(
                      "You have unsaved changes. Do you really want to close without saving?",
                    );
                    if (confirmLeave) handleOpenChange(false);
                  }
                }}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </SheetHeader>

        <div className="flex h-[calc(100vh-8rem)] overflow-hidden w-full">
          {/* Editor Section */}
          <div
            className={cn(
              "flex-1 min-w-0 transition-all duration-300 ease-in-out",
              isPanelOpen ? "mr-4" : "mr-0",
            )}
          >
            <DocumentEditor
              document={localDoc}
              organizationId={organizationId}
              onSave={handleSave}
              onCancel={handleCancel}
              triggerSave={triggerSave}
              sessionId={editorSessionId}
            />
          </div>

          {/* Details Panel */}
          <div
            className={cn(
              "transition-all duration-300 ease-in-out overflow-hidden flex-shrink-0",
              isPanelOpen ? "w-80 opacity-100" : "w-0 opacity-0",
            )}
          >
            {isPanelOpen && (
              <DocumentDetailsPanel
                document={localDoc}
                className="h-full w-80"
                onLocalUpdate={(updated) => {
                  // Update local shadow state so details panel reflects immediately
                  setLocalDoc((prev) => ({ ...prev, ...updated }));
                  // Bubble event upward so table row can update without refetch
                  onLocalUpdate?.(updated);
                }}
              />
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};
