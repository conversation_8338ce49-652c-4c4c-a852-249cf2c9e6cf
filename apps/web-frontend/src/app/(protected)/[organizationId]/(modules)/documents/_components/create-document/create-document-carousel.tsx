"use client";

import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { documentTypeIcons, DocumentTypeIconKey } from "./document-type-icons";
import { createDocument } from "../../_actions/create-document";
import { ContractsV1Files } from "@askinfosec/types";

interface DocumentType {
  key: DocumentTypeIconKey;
  label: string;
  description: string;
  documentType?: ContractsV1Files.DocumentType;
}

const documentTypes: DocumentType[] = [
  {
    key: "blank",
    label: "Blank Document",
    description: "Start with a blank document",
    documentType: undefined, // null for blank documents
  },
  {
    key: "policy",
    label: "Policy",
    description: "Create a policy document",
    documentType: "policy",
  },
  {
    key: "procedure",
    label: "Procedure",
    description: "Create a procedure document",
    documentType: "procedure",
  },
  {
    key: "note",
    label: "Note",
    description: "Create a note or memo",
    documentType: "document",
  },
];

interface CreateDocumentCarouselProps {
  organizationId: string;
  onDocumentCreated?: (document: ContractsV1Files.FileDTO) => void;
}

export const CreateDocumentCarousel: React.FC<CreateDocumentCarouselProps> = ({
  organizationId,
  onDocumentCreated,
}) => {
  const [creatingType, setCreatingType] = useState<DocumentTypeIconKey | null>(null);

  const handleCreateDocument = async (docType: DocumentType) => {
    if (creatingType) return; // Prevent multiple simultaneous creations

    setCreatingType(docType.key);

    try {
      const result = await createDocument({
        organizationId,
        name: `Untitled ${docType.label}`,
        documentType: docType.documentType,
      });

      if (result.success && result.document) {
        toast.success(`${docType.label} created successfully!`);
        onDocumentCreated?.(result.document);
      } else {
        toast.error(result.message || `Failed to create ${docType.label.toLowerCase()}`);
      }
    } catch (error) {
      console.error("Error creating document:", error);
      toast.error(`Failed to create ${docType.label.toLowerCase()}`);
    } finally {
      setCreatingType(null);
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div>
        <p className="text-md font-semibold text-gray-900 dark:text-white mb-4">
          Start a new document
        </p>
      </div>

      <Carousel
        opts={{
          align: "start",
          loop: false,
        }}
        className="w-full"
      >
        <CarouselContent className="-ml-2 md:-ml-4">
          {documentTypes.map((docType) => {
            const IconComponent = documentTypeIcons[docType.key];
            const isCreating = creatingType === docType.key;

            return (
              <CarouselItem
                key={docType.key}
                className="pl-2 md:pl-4 basis-1/2 md:basis-1/3 lg:basis-1/4"
              >
                <Card 
                  className={cn(
                    "h-full cursor-pointer transition-colors",
                    "hover:bg-gray-50 dark:hover:bg-gray-800",
                    "border-2 border-transparent hover:border-blue-200 dark:hover:border-blue-800",
                    isCreating && "opacity-75 cursor-not-allowed",
                  )}
                  onClick={() => !isCreating && !creatingType && handleCreateDocument(docType)}
                >
                  <CardContent 
                    className={cn(
                      "p-6 flex flex-col items-center justify-center space-y-3",
                      "min-h-[160px]",
                    )}
                  >
                    {isCreating ? (
                      <Loader2 className="h-12 w-12 animate-spin text-blue-500" />
                    ) : (
                      <IconComponent size={64} className="mb-2" />
                    )}

                    <div className="text-center">
                      <h3 className="font-medium text-sm text-gray-900 dark:text-white">
                        {docType.label}
                      </h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {isCreating ? "Creating..." : docType.description}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </CarouselItem>
            );
          })}
        </CarouselContent>
        <CarouselPrevious className="hidden md:flex" />
        <CarouselNext className="hidden md:flex" />
      </Carousel>
    </div>
  );
};
