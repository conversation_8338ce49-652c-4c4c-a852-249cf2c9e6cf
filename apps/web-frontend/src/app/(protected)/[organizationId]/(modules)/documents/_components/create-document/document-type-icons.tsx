import React from "react";

interface IconProps {
  className?: string;
  size?: number;
}

export const BlankDocumentIcon: React.FC<IconProps> = ({ className = "", size = 48 }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <rect
      x="8"
      y="6"
      width="32"
      height="36"
      rx="2"
      fill="#f8fafc"
      stroke="#e2e8f0"
      strokeWidth="2"
    />
    <rect x="12" y="14" width="24" height="2" rx="1" fill="#cbd5e1" />
    <rect x="12" y="20" width="20" height="2" rx="1" fill="#cbd5e1" />
    <rect x="12" y="26" width="24" height="2" rx="1" fill="#cbd5e1" />
    <rect x="12" y="32" width="16" height="2" rx="1" fill="#cbd5e1" />
  </svg>
);

export const PolicyIcon: React.FC<IconProps> = ({ className = "", size = 48 }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <rect
      x="8"
      y="6"
      width="32"
      height="36"
      rx="2"
      fill="#fef3c7"
      stroke="#f59e0b"
      strokeWidth="2"
    />
    <path d="M16 16h16v2H16v-2zm0 6h12v2H16v-2zm0 6h16v2H16v-2zm0 6h10v2H16v-2z" fill="#d97706" />
    <circle cx="34" cy="14" r="6" fill="#dc2626" />
    <path
      d="M31 14l2 2 4-4"
      stroke="white"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ProcedureIcon: React.FC<IconProps> = ({ className = "", size = 48 }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <rect
      x="8"
      y="6"
      width="32"
      height="36"
      rx="2"
      fill="#dbeafe"
      stroke="#3b82f6"
      strokeWidth="2"
    />
    <circle cx="14" cy="16" r="2" fill="#3b82f6" />
    <rect x="18" y="15" width="16" height="2" rx="1" fill="#1e40af" />
    <circle cx="14" cy="24" r="2" fill="#3b82f6" />
    <rect x="18" y="23" width="12" height="2" rx="1" fill="#1e40af" />
    <circle cx="14" cy="32" r="2" fill="#3b82f6" />
    <rect x="18" y="31" width="14" height="2" rx="1" fill="#1e40af" />
    <path
      d="M20 16l2 2 4-4"
      stroke="white"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const NoteIcon: React.FC<IconProps> = ({ className = "", size = 48 }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 48 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <rect
      x="8"
      y="6"
      width="32"
      height="36"
      rx="2"
      fill="#f0fdf4"
      stroke="#22c55e"
      strokeWidth="2"
    />
    <path d="M32 6v8l4-2 4 2V6" fill="#22c55e" />
    <rect x="12" y="16" width="20" height="2" rx="1" fill="#16a34a" />
    <rect x="12" y="22" width="24" height="2" rx="1" fill="#16a34a" />
    <rect x="12" y="28" width="18" height="2" rx="1" fill="#16a34a" />
    <rect x="12" y="34" width="22" height="2" rx="1" fill="#16a34a" />
  </svg>
);

// Export all icons as a map for easy access
export const documentTypeIcons = {
  blank: BlankDocumentIcon,
  policy: PolicyIcon,
  procedure: ProcedureIcon,
  note: NoteIcon,
} as const;

export type DocumentTypeIconKey = keyof typeof documentTypeIcons;
