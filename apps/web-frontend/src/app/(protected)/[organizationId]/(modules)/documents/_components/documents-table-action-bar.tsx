"use client";

import type { Table } from "@tanstack/react-table";
import { Download, Trash2 } from "lucide-react";
import * as React from "react";
import { toast } from "sonner";
import {
  DataTableActionBar,
  DataTableActionBarAction,
  DataTableActionBarSelection,
} from "@/components/data-table/data-table-action-bar";
import { ContractsV1Files } from "@askinfosec/types";

interface DocumentsTableActionBarProps {
  table: Table<ContractsV1Files.FileDTO>;
  onDeleteSelected: () => void;
  onDownloadSelected: () => void;
}

export function DocumentsTableActionBar({
  table,
  onDeleteSelected,
  onDownloadSelected,
}: DocumentsTableActionBarProps) {
  const rows = table.getFilteredSelectedRowModel().rows;
  const [isPending, startTransition] = React.useTransition();
  const [currentAction, setCurrentAction] = React.useState<string | null>(null);

  // Debug logging
  React.useEffect(() => {
    console.log("DocumentsTableActionBar - rows changed:", {
      rowCount: rows.length,
      selectedRows: rows.map((row) => ({
        id: row.original.id,
        name: row.original.name,
      })),
      tableState: table.getState().rowSelection,
    });
  }, [rows, table]);

  const getIsActionPending = React.useCallback(
    (action: string) => isPending && currentAction === action,
    [isPending, currentAction],
  );

  const handleDownloadSelected = React.useCallback(() => {
    setCurrentAction("download");
    startTransition(() => {
      try {
        onDownloadSelected();
        toast.success("Download initiated");
      } catch (error) {
        toast.error("Failed to download documents");
        console.error("Download error:", error);
      } finally {
        setCurrentAction(null);
      }
    });
  }, [onDownloadSelected]);

  const handleDeleteSelected = React.useCallback(() => {
    setCurrentAction("delete");
    startTransition(() => {
      try {
        onDeleteSelected();
        toast.success("Documents deleted successfully");
      } catch (error) {
        toast.error("Failed to delete documents");
        console.error("Delete error:", error);
      } finally {
        setCurrentAction(null);
      }
    });
  }, [onDeleteSelected]);

  // Debug: Log what we're passing to DataTableActionBar
  console.log("DocumentsTableActionBar render:", {
    selectedRowCount: rows.length,
    tableRowSelection: table.getState().rowSelection,
    shouldShow: rows.length > 0,
    rows: rows.map((row) => ({ id: row.original.id, name: row.original.name })),
  });

  return (
    <DataTableActionBar table={table}>
      <DataTableActionBarSelection table={table} />
      <DataTableActionBarAction
        onClick={handleDownloadSelected}
        tooltip="Download selected documents"
        isPending={getIsActionPending("download")}
      >
        <Download className="h-4 w-4" />
        Download
      </DataTableActionBarAction>
      <DataTableActionBarAction
        onClick={handleDeleteSelected}
        tooltip="Delete selected documents"
        isPending={getIsActionPending("delete")}
      >
        <Trash2 className="h-4 w-4" />
        Delete
      </DataTableActionBarAction>
    </DataTableActionBar>
  );
}
