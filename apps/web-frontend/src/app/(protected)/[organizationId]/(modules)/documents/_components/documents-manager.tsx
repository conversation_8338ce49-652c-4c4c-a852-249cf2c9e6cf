"use client";

import React, { useState, useRef } from "react";
import { DocumentsDataTable, DocumentsDataTableRef } from "./documents-data-table";
import { CreateDocumentCarousel } from "./create-document";
import { ContractsV1Files } from "@askinfosec/types";

interface DocumentsManagerProps {
  organizationId: string;
}

export const DocumentsManager: React.FC<DocumentsManagerProps> = ({ organizationId }) => {
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const dataTableRef = useRef<DocumentsDataTableRef>(null);

  const handleDocumentCreated = (document: ContractsV1Files.FileDTO) => {
    // Trigger a refresh of the data table to include the new document
    setRefreshTrigger((prev) => prev + 1);

    // Open the newly created document in the editor
    // We'll need to add a small delay to ensure the data table has refreshed
    setTimeout(() => {
      dataTableRef.current?.openDocument(document.id);
    }, 100);
  };

  return (
    <div className="space-y-8">
      <CreateDocumentCarousel
        organizationId={organizationId}
        onDocumentCreated={handleDocumentCreated}
      />
      <DocumentsDataTable
        organizationId={organizationId}
        key={refreshTrigger} // Force re-render when new document is created
        ref={dataTableRef}
      />
    </div>
  );
};
