"use client";

import React, { useRef } from "react";
import { DocumentsDataTable, DocumentsDataTableRef } from "./documents-data-table";
import { CreateDocumentCarousel } from "./create-document";
import { ContractsV1Files } from "@askinfosec/types";

interface DocumentsManagerProps {
  organizationId: string;
}

export const DocumentsManager: React.FC<DocumentsManagerProps> = ({ organizationId }) => {
  const dataTableRef = useRef<DocumentsDataTableRef>(null);

  const handleDocumentCreated = (document: ContractsV1Files.FileDTO) => {
    // Add the document to the data table immediately
    dataTableRef.current?.addDocument(document);

    // Open the newly created document in the editor
    // Small delay to ensure the document is added to the state
    setTimeout(() => {
      dataTableRef.current?.openDocument(document.id);
    }, 50);
  };

  return (
    <>
      <CreateDocumentCarousel
        organizationId={organizationId}
        onDocumentCreated={handleDocumentCreated}
      />
      <DocumentsDataTable organizationId={organizationId} ref={dataTableRef} />
    </>
  );
};
