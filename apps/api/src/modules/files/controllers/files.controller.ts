import {
  Controller,
  Get,
  Query,
  Param,
  Req,
  UseGuards,
  BadRequestException,
  Put,
  Body,
  Post,
  Delete,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { FilesService, GetFilesFilters } from '../services/files.service';
import { DeleteFilesDto } from '../dto/delete-files.dto';
import { ContractsV1Files } from '@askinfosec/types/';
import { CustomAuthGuard } from '../../auth/guards/custom-auth.guard';
import { RlsContextGuard } from '../../db-drizzle/guards/rls-context.guard';
import { ApiSurface } from '../../../common/decorators/api-surface.decorator';
import { Version } from '../../../common/decorators/version.decorator';
import type { ExtendedRequest } from '../../../common/types/extended-request';

@ApiTags('Files')
@ApiBearerAuth()
@ApiSurface('internal')
@Version('1')
@Controller('organizations/:organizationId/files')
@UseGuards(CustomAuthGuard, RlsContextGuard)
export class FilesController {
  constructor(private readonly filesService: FilesService) {}

  @Get()
  @ApiOperation({
    summary: 'Get organization files with filters and pagination',
    description:
      'Retrieve files for an organization with optional filtering, sorting, and pagination',
  })
  @ApiResponse({
    status: 200,
    description: 'Files retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: { $ref: '#/components/schemas/FileDTO' },
        },
        stats: { $ref: '#/components/schemas/FileStatsDTO' },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        hasMore: { type: 'boolean' },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 20)',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Sort field (default: createdAt)',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['asc', 'desc'],
    description: 'Sort order (default: desc)',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search in file names',
  })
  @ApiQuery({
    name: 'documentType',
    required: false,
    type: [String],
    description: 'Filter by document types',
  })
  @ApiQuery({
    name: 'accessLevel',
    required: false,
    type: [String],
    description: 'Filter by access levels',
  })
  @ApiQuery({
    name: 'categoryId',
    required: false,
    type: [String],
    description: 'Filter by category IDs',
  })
  @ApiQuery({
    name: 'scopeId',
    required: false,
    type: [String],
    description: 'Filter by scope IDs',
  })
  @ApiQuery({
    name: 'fileType',
    required: false,
    type: [String],
    description: 'Filter by file types',
  })
  @ApiQuery({
    name: 'createdById',
    required: false,
    type: [String],
    description: 'Filter by creator IDs',
  })
  @ApiQuery({
    name: 'includeTrained',
    required: false,
    type: Boolean,
    description: 'Include only trained files',
  })
  @ApiQuery({
    name: 'excludeTrained',
    required: false,
    type: Boolean,
    description: 'Exclude trained files',
  })
  @ApiQuery({
    name: 'createdAfter',
    required: false,
    type: String,
    description: 'Filter files created after date (ISO string)',
  })
  @ApiQuery({
    name: 'createdBefore',
    required: false,
    type: String,
    description: 'Filter files created before date (ISO string)',
  })
  async getFiles(
    @Param('organizationId') organizationId: string,
    @Req() request: ExtendedRequest,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'asc' | 'desc',
    @Query('search') search?: string,
    @Query('documentType') documentType?: string | string[],
    @Query('accessLevel') accessLevel?: string | string[],
    @Query('categoryId') categoryId?: string | string[],
    @Query('scopeId') scopeId?: string | string[],
    @Query('fileType') fileType?: string | string[],
    @Query('createdById') createdById?: string | string[],
    @Query('includeTrained') includeTrained?: boolean,
    @Query('excludeTrained') excludeTrained?: boolean,
    @Query('createdAfter') createdAfter?: string,
    @Query('createdBefore') createdBefore?: string,
  ): Promise<ContractsV1Files.GetFilesResponse> {
    // Validate pagination parameters
    const validatedPage = Math.max(1, page || 1);
    const validatedLimit = Math.min(100, Math.max(1, limit || 20));

    // Build session context
    const context = {
      organizationId,
      userId: request.userId || 'system',
      bypassRls: request.bypassRls || false,
    };

    // Build filters
    const filters: GetFilesFilters = {};
    if (search) filters.search = search;
    if (documentType)
      filters.documentType = Array.isArray(documentType) ? documentType : [documentType];
    if (accessLevel) filters.accessLevel = Array.isArray(accessLevel) ? accessLevel : [accessLevel];
    if (categoryId) filters.categoryId = Array.isArray(categoryId) ? categoryId : [categoryId];
    if (scopeId) filters.scopeId = Array.isArray(scopeId) ? scopeId : [scopeId];
    if (fileType) filters.fileType = Array.isArray(fileType) ? fileType : [fileType];
    if (createdById) filters.createdById = Array.isArray(createdById) ? createdById : [createdById];
    if (includeTrained !== undefined) filters.includeTrained = includeTrained;
    if (excludeTrained !== undefined) filters.excludeTrained = excludeTrained;
    if (createdAfter) filters.createdAfter = createdAfter;
    if (createdBefore) filters.createdBefore = createdBefore;

    // Validate date filters
    if (createdAfter && isNaN(Date.parse(createdAfter))) {
      throw new BadRequestException('Invalid createdAfter date format');
    }
    if (createdBefore && isNaN(Date.parse(createdBefore))) {
      throw new BadRequestException('Invalid createdBefore date format');
    }

    // Get files from service
    const result = await this.filesService.getFiles(
      context,
      filters,
      validatedPage,
      validatedLimit,
    );

    // Build response
    const response: ContractsV1Files.GetFilesResponse = {
      files: result.files,
      stats: result.stats,
      total: result.total,
      page: validatedPage,
      limit: validatedLimit,
      hasMore: validatedPage * validatedLimit < result.total,
    };

    // Validate response against schema
    const validatedResponse = ContractsV1Files.GetFilesResponseSchema.parse(response);

    return validatedResponse;
  }

  @Get(':id/download')
  @ApiOperation({
    summary: 'Get a file by ID',
    description: 'Retrieve a file by its unique identifier and download it',
  })
  @ApiResponse({
    status: 200,
    description: 'File retrieved successfully and downloaded',
  })
  @ApiResponse({ status: 404, description: 'File not found' })
  async downloadFile(
    @Param('id') id: string,
    @Req() request: ExtendedRequest,
  ): Promise<ContractsV1Files.FileDTO> {
    const context = {
      organizationId: request.organizationId,
      userId: request.userId,
      bypassRls: request.bypassRls || false,
    };
    const file = await this.filesService.getFileByIdForDownload(id, context);
    return file;
  }

  // @Post()
  // @ApiOperation({
  //   summary: 'Create a new file',
  //   description: 'Create a new file in the organization',
  // })
  // @ApiResponse({ status: 201, description: 'File created successfully' })
  // @ApiResponse({ status: 400, description: 'Bad request' })
  // async createFile(@Body() file: ContractsV1Files.FileDTO): Promise<ContractsV1Files.FileDTO> {
  //   return this.filesService.createFile(file);
  // }
  @Put(':id')
  @ApiOperation({
    summary: 'Update a file',
    description: 'Update a file in the organization',
  })
  @ApiResponse({ status: 200, description: 'File updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'File not found' })
  async updateFile(
    @Param('id') id: string,
    @Body() data: ContractsV1Files.FileUpdateRequest,
    @Req() request: ExtendedRequest,
  ): Promise<ContractsV1Files.FileUpdateResponse> {
    const context = {
      organizationId: request.organizationId,
      userId: request.userId,
      bypassRls: request.bypassRls || false,
    };
    return this.filesService.updateFile(id, data, context);
  }

  @Post()
  @ApiOperation({
    summary: 'Create a new file',
    description: 'Create a new file in the organization',
  })
  @ApiResponse({ status: 201, description: 'File created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async createFile(
    @Body() data: ContractsV1Files.FileDTO,
    @Req() request: ExtendedRequest,
  ): Promise<ContractsV1Files.FileDTO> {
    const context = {
      organizationId: request.organizationId,
      userId: request.userId,
      bypassRls: request.bypassRls || false,
    };
    return this.filesService.createFile(data, context);
  }

  @Delete()
  @ApiOperation({
    summary: 'Delete files',
    description: 'Delete files in the organization',
  })
  @ApiResponse({ status: 200, description: 'Files deleted successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async deleteFiles(
    @Body() body: DeleteFilesDto,
    @Req() request: ExtendedRequest,
  ): Promise<boolean> {
    const context = {
      organizationId: request.organizationId,
      userId: request.userId,
      bypassRls: request.bypassRls || false,
    };
    return this.filesService.deleteFiles(body.ids, context);
  }
}
