import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { FileRepository, SessionContext, File, FileFilters } from '@askinfosec/database-drizzle';
import { ContractsV1Files } from '@askinfosec/types';

export interface GetFilesFilters {
  search?: string;
  documentType?: string[];
  accessLevel?: string[];
  categoryId?: string[];
  scopeId?: string[];
  fileType?: string[];
  createdById?: string[];
  includeTrained?: boolean;
  excludeTrained?: boolean;
  createdAfter?: string;
  createdBefore?: string;
}

@Injectable()
export class FilesService {
  private readonly logger = new Logger(FilesService.name);

  constructor(private readonly fileRepository: FileRepository) {}

  async getFiles(
    context: SessionContext,
    filters?: GetFilesFilters,
    page: number = 1,
    limit: number = 20,
  ): Promise<{
    files: ContractsV1Files.FileDTO[];
    total: number;
    stats: ContractsV1Files.FileStatsDTO;
  }> {
    this.logger.debug(`Getting files for organization: ${context.organizationId}`);

    // Convert API filters to repository filters
    const repositoryFilters: FileFilters = {};
    if (filters) {
      repositoryFilters.name = filters.search;
      repositoryFilters.documentType = filters.documentType?.[0];
      repositoryFilters.accessLevel = filters.accessLevel?.[0];
      repositoryFilters.categoryId = filters.categoryId?.[0];
      repositoryFilters.scopeId = filters.scopeId?.[0];
      repositoryFilters.fileType = filters.fileType?.[0];
      repositoryFilters.createdById = filters.createdById?.[0];
      repositoryFilters.includeTrained = filters.includeTrained;
      repositoryFilters.excludeTrained = filters.excludeTrained;
      repositoryFilters.createdAfter = filters.createdAfter;
      repositoryFilters.createdBefore = filters.createdBefore;
    }

    // Get files from repository
    const { files, total } = await this.fileRepository.findByOrganization(
      context,
      repositoryFilters,
      page,
      limit,
    );

    // Transform files to DTOs (without buffer data for listing)
    const fileDTOs = files.map((file) => this.transformFileToDTO(file, false));

    // Calculate stats
    const stats = this.calculateFileStats(files);

    return {
      files: fileDTOs,
      total,
      stats,
    };
  }

  private transformFileToDTO(file: File, includeBuffer: boolean = false): ContractsV1Files.FileDTO {
    return {
      id: file.id,
      name: file.name,
      path: file.path || undefined,
      bufferFile:
        includeBuffer && file.bufferFile
          ? Buffer.isBuffer(file.bufferFile)
            ? file.bufferFile.toString('base64')
            : file.bufferFile
          : undefined,
      checksum: file.checksum || undefined,
      organizationId: file.organizationId,
      createdById: file.createdById || undefined,
      createdAt: file.createdAt.toISOString(),
      updatedAt: file.updatedAt.toISOString(),
      trainedAt: file.trainedAt?.toISOString(),
      documentType: (file.documentType as ContractsV1Files.DocumentType) || undefined,
      categoryId: file.categoryId || undefined,
      scopeId: file.scopeId || undefined,
      accessLevel: (file.accessLevel as ContractsV1Files.FileAccessLevel) || undefined,
      tags: file.tags || [],
      fileType: file.fileType || undefined,
      content: file.content || undefined,
      url: file.url || undefined,
      notes: file.notes || undefined,
      // Note: Relations would need to be included in the repository query
      // For now, we'll leave them undefined as the repository doesn't include relations
      createdBy: undefined,
      scope: undefined,
    };
  }

  private calculateFileStats(files: File[]): ContractsV1Files.FileStatsDTO {
    const stats: ContractsV1Files.FileStatsDTO = {
      total: files.length,
      byDocumentType: {},
      byAccessLevel: {},
      trained: 0,
      untrained: 0,
      recentUploads: 0,
    };

    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    files.forEach((file) => {
      // Count by document type
      if (file.documentType) {
        stats.byDocumentType![file.documentType] =
          (stats.byDocumentType![file.documentType] || 0) + 1;
      }

      // Count by access level
      if (file.accessLevel) {
        stats.byAccessLevel![file.accessLevel] = (stats.byAccessLevel![file.accessLevel] || 0) + 1;
      }

      // Count trained/untrained
      if (file.trainedAt) {
        stats.trained!++;
      } else {
        stats.untrained!++;
      }

      // Count recent uploads (last 7 days)
      if (file.createdAt > sevenDaysAgo) {
        stats.recentUploads!++;
      }
    });

    return stats;
  }

  async getFileByIdForDownload(
    id: string,
    context: SessionContext,
  ): Promise<ContractsV1Files.FileDTO> {
    const file = await this.fileRepository.findById(id, context);
    if (!file) {
      throw new NotFoundException('File not found');
    }
    return this.transformFileToDTO(file, true); // Include buffer data for download
  }

  async updateFile(
    fileId: string,
    data: ContractsV1Files.FileUpdateRequest,
    context: SessionContext,
  ): Promise<ContractsV1Files.FileUpdateResponse> {
    const file = await this.fileRepository.updateFile(fileId, data, context);
    if (!file) {
      throw new NotFoundException('File not found');
    }
    return {
      success: true,
      message: 'File updated successfully',
      updatedFile: this.transformFileToDTO(file, false),
    };
  }

  async createFile(
    data: ContractsV1Files.FileDTO,
    context: SessionContext,
  ): Promise<ContractsV1Files.FileDTO> {
    const file = await this.fileRepository.createFile(data, context);
    if (!file) {
      throw new BadRequestException('File not created');
    }
    return this.transformFileToDTO(file, false);
  }

  async deleteFiles(fileIds: string[], context: SessionContext): Promise<boolean> {
    return this.fileRepository.deleteFiles(fileIds, context);
  }
}
