import { eq, and, like, desc, count, isNotNull, isNull, gte, lte } from 'drizzle-orm';
import { BaseRepository, SessionContext, IdGenerationConfig } from '../lib/base-repository.js';
import { 
  files,
  type File, 
  type NewFile 
} from '../schemas/schema.js';

export interface FileFilters {
  name?: string;
  documentType?: string;
  accessLevel?: string;
  categoryId?: string;
  scopeId?: string;
  fileType?: string;
  createdById?: string;
  includeTrained?: boolean;
  excludeTrained?: boolean;
  createdAfter?: string;
  createdBefore?: string;
}

export interface CreateFileData {
  name: string;
  documentType?: string;
}

export interface UpdateFileData {
  name?: string;
  path?: string;
  bufferFile?: string;
  checksum?: string;
  documentType?: string;
  categoryId?: string;
  scopeId?: string;
  accessLevel?: string;
  tags?: string[];
  fileType?: string;
  content?: string;
  contentChangeHistory?: any;
  url?: string;
  notes?: string;
  otherData?: any;
  aiSettings?: any;
  auditLog?: any;
  trainedAt?: Date;
}

export class FileRepository extends BaseRepository {
  protected readonly idConfig: IdGenerationConfig = {
    usePrefix: false,
    prefix: 'file'
  };
  
  private validateContext(context: SessionContext, requiredFields: (keyof SessionContext)[] = ['organizationId']): void {
    if (!context) {
      throw new Error('Session context is required');
    }
    
    for (const field of requiredFields) {
      if (!context[field]) {
        throw new Error(`${field} is required in session context`);
      }
    }
  }

  async findById(id: string, context: SessionContext): Promise<File | null> {
    this.validateContext(context);
    
    return this.executeQueryWithSession(async (tx) => {
      const result = await tx
        .select()
        .from(files)
        .where(eq(files.id, id))
        .limit(1);
      
      return result[0] || null;
    }, context, 'findById');
  }

  async findByChecksum(checksum: string, context: SessionContext): Promise<File | null> {
    this.validateContext(context);
    
    return this.executeQueryWithSession(async (tx) => {
      const result = await tx
        .select()
        .from(files)
        .where(and(
          eq(files.checksum, checksum),
          eq(files.organizationId, context.organizationId as string)
        ))
        .limit(1);
      
      return result[0] || null;
    }, context, 'findByChecksum');
  }

  async findByOrganization(
    context: SessionContext,
    filters?: FileFilters,
    page: number = 1,
    limit: number = 20,
  ): Promise<{ files: File[]; total: number }> {
    this.validateContext(context);
    const offset = (page - 1) * limit;
    
    return this.executeQueryWithSession(async (tx) => {
      // Build where conditions
      const whereConditions = [
        eq(files.organizationId, context.organizationId as string)
      ];
      
      if (filters?.name) {
        const nameLower = filters.name.toLowerCase();
        whereConditions.push(like(files.name, `%${nameLower}%`));
      }
      
      if (filters?.documentType) {
        whereConditions.push(eq(files.documentType, filters.documentType));
      }
      
      if (filters?.accessLevel) {
        whereConditions.push(eq(files.accessLevel, filters.accessLevel));
      }
      
      if (filters?.categoryId) {
        whereConditions.push(eq(files.categoryId, filters.categoryId));
      }
      
      if (filters?.scopeId) {
        whereConditions.push(eq(files.scopeId, filters.scopeId));
      }
      
      if (filters?.fileType) {
        whereConditions.push(eq(files.fileType, filters.fileType));
      }
      
      if (filters?.createdById) {
        whereConditions.push(eq(files.createdById, filters.createdById));
      }
      
      if (filters?.includeTrained) {
        whereConditions.push(isNotNull(files.trainedAt)); // Files that ARE trained
      }
      
      if (filters?.excludeTrained) {
        whereConditions.push(isNull(files.trainedAt)); // Files that are NOT trained
      }
      
      if (filters?.createdAfter) {
        whereConditions.push(gte(files.createdAt, new Date(filters.createdAfter)));
      }
      
      if (filters?.createdBefore) {
        whereConditions.push(lte(files.createdAt, new Date(filters.createdBefore)));
      }

      // Get total count
      const countResult = await tx
        .select({ count: count() })
        .from(files)
        .where(and(...whereConditions));

      const total = countResult[0]?.count || 0;

      // Get paginated results
      const result = await tx
        .select()
        .from(files)
        .where(and(...whereConditions))
        .orderBy(desc(files.createdAt))
        .limit(limit)
        .offset(offset);

      return {
        files: result,
        total,
      };
    }, context, 'findByOrganization');
  }

  async findByDocumentType(documentType: string, context: SessionContext): Promise<File[]> {
    this.validateContext(context);
    
    return this.executeQueryWithSession(async (tx) => {
      const result = await tx
        .select()
        .from(files)
        .where(and(
          eq(files.documentType, documentType),
          eq(files.organizationId, context.organizationId as string)
        ))
        .orderBy(desc(files.createdAt));

      return result;
    }, context, 'findByDocumentType');
  }

  async findByCategory(categoryId: string, context: SessionContext): Promise<File[]> {
    this.validateContext(context);
    
    return this.executeQueryWithSession(async (tx) => {
      const result = await tx
        .select()
        .from(files)
        .where(and(
          eq(files.categoryId, categoryId),
          eq(files.organizationId, context.organizationId as string)
        ))
        .orderBy(desc(files.createdAt));

      return result;
    }, context, 'findByCategory');
  }

  async findByCreator(createdById: string, context: SessionContext): Promise<File[]> {
    this.validateContext(context);
    
    return this.executeQueryWithSession(async (tx) => {
      const result = await tx
        .select()
        .from(files)
        .where(and(
          eq(files.createdById, createdById),
          eq(files.organizationId, context.organizationId as string)
        ))
        .orderBy(desc(files.createdAt));

      return result;
    }, context, 'findByCreator');
  }

  async searchFiles(searchTerm: string, context: SessionContext): Promise<File[]> {
    this.validateContext(context);
    
    return this.executeQueryWithSession(async (tx) => {
      const searchLower = searchTerm.toLowerCase();
      const result = await tx
        .select()
        .from(files)
        .where(and(
          eq(files.organizationId, context.organizationId as string),
          like(files.name, `%${searchLower}%`)
        ))
        .orderBy(desc(files.createdAt));

      return result;
    }, context, 'searchFiles');
  }

  async getFileCount(context: SessionContext): Promise<number> {
    this.validateContext(context);
    
    return this.executeQueryWithSession(async (tx) => {
      const result = await tx
        .select({ count: count() })
        .from(files)
        .where(eq(files.organizationId, context.organizationId as string));

      return result[0]?.count || 0;
    }, context, 'getFileCount');
  }

  async getFileCountByType(fileType: string, context: SessionContext): Promise<number> {
    this.validateContext(context);
    
    return this.executeQueryWithSession(async (tx) => {
      const result = await tx
        .select({ count: count() })
        .from(files)
        .where(and(
          eq(files.fileType, fileType),
          eq(files.organizationId, context.organizationId as string)
        ));

      return result[0]?.count || 0;
    }, context, 'getFileCountByType');
  }

  async getFileCountByDocumentType(documentType: string, context: SessionContext): Promise<number> {
    this.validateContext(context);
    
    return this.executeQueryWithSession(async (tx) => {
      const result = await tx
        .select({ count: count() })
        .from(files)
        .where(and(
          eq(files.documentType, documentType),
          eq(files.organizationId, context.organizationId as string)
        ));

      return result[0]?.count || 0;
    }, context, 'getFileCountByDocumentType');
  }

  async createFile(data: CreateFileData, context: SessionContext): Promise<File> {
    this.validateContext(context, ['organizationId', 'userId']);
    const createdBy = context.userId;
    const createdAt = new Date();
    return this.executeQueryWithSession(async (tx) => {
      const result = await tx.insert(files).values({ id: this.generateId(), name: data.name || "Untitled", documentType: data.documentType || null, organizationId: context.organizationId as string, createdById: createdBy, createdAt, updatedAt: createdAt }).returning();
      return result[0] || null;
    }, context, 'createFile');
  }

  async updateFile(fileId: string, data: UpdateFileData, context: SessionContext): Promise<File | null> {
    this.validateContext(context, ['organizationId', 'userId']);
    const updatedBy = context.userId;
    const updatedAt = new Date();
    // Build dynamic update payload: include only fields present (not undefined)
    // This allows callers to update any subset of fields safely
    return this.executeQueryWithSession(async (tx) => {
      const updatePayload: Record<string, unknown> = { updatedAt };

      // Copy defined fields from the DTO
      for (const [key, value] of Object.entries(data)) {
        if (value !== undefined && key !== 'contentChangeHistory') {
          updatePayload[key] = value as unknown;
        }
      }

      // Maintain content change history when content is updated
      if (data.content !== undefined) {
        const historyEntry = { version: 1, content: data.content, updatedAt, updatedBy };
        const history = Array.isArray(data.contentChangeHistory)
          ? [...data.contentChangeHistory, historyEntry]
          : [historyEntry];
        updatePayload['contentChangeHistory'] = history;
      }

      const result = await tx
        .update(files)
        .set(updatePayload)
        .where(and(
          eq(files.id, fileId),
          eq(files.organizationId, context.organizationId as string)
        ))
        .returning();
  
      return result[0] || null;
    }, context, 'updateFile');
  }
}
